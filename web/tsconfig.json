{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowJs": true, "checkJs": false, "types": ["node"], "baseUrl": ".", "paths": {"@trustsync/types": ["../packages/trustsync-types/dist"], "@trustsync/types/*": ["../packages/trustsync-types/dist/*"]}}, "include": ["*.js", "app/**/*.js", "**/*.ts", "eslint.config.js"], "exclude": ["node_modules", "frontend", "dist", "build"]}