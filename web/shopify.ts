import { ApiVersion } from "@shopify/shopify-api";
// @ts-ignore - TypeScript module resolution issue with pnpm
import { restResources } from "@shopify/shopify-api/rest/admin/2024-10";
import { shopifyApp } from "@shopify/shopify-app-express";
import { RedisSessionStorage } from "@shopify/shopify-app-session-storage-redis";

import dotenv from "dotenv";
dotenv.config();

const shopify = shopifyApp({
  api: {
    apiVersion: ApiVersion.October24,
    restResources,
    future: {
      customerAddressDefaultFix: true,
      lineItemBilling: true,
      unstable_managedPricingSupport: true,
    },
  },
  auth: {
    path: "/api/auth",
    callbackPath: "/api/auth/callback",
  },
  webhooks: {
    path: "/webhooks",
  },
  sessionStorage: RedisSessionStorage.withCredentials(
    process.env.REDIS_HOST as string,
    parseInt(process.env.REDIS_DATABASE as string, 10),
    process.env.REDIS_USERNAME as string,
    process.env.REDIS_PASSWORD as string,
    {}
  ),
});

export default shopify;
