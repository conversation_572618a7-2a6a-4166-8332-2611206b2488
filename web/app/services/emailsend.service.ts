import { addDays } from "date-fns";
import cron from "node-cron";
import pLimit from "p-limit";
import Email from "../models/Email.js";
import EmailSettings, { IEmailSettingsDocument } from "../models/EmailSettings.js";
import Shop, { IShopDocument } from "../models/Shop.js";
import ShopEmails from "../models/ShopEmails.js";
import Subscription from "../models/Subscription.js";
import { sleep } from "../utils/helper.js";
import { handleSendEmail, handleWeeklyReportEmail, MailgunResponse } from "./mailgun.service.js";

// constants
const BATCH_SIZE = 50;
const CONCURRENCY_LIMIT = 5;
const SEVEN_DAYS_MS = 7 * 24 * 60 * 60 * 1000;

// Interface for test email response
interface TestEmailResponse {
  status: boolean;
  message: string;
}

/**
 * Send a test email to the given email address or the shop owner's email
 */
export const sendTestEmail = async (shopDB: string, to?: string): Promise<TestEmailResponse | undefined> => {
  try {
    const shop = await Shop.findOne({ shop: shopDB });
    const emailSetting = await EmailSettings.findOne({ shop: shopDB });

    if (!shop || !emailSetting) {
      return { status: false, message: "Shop or email settings not found" };
    }

    const email = {
      email: to || shop?.info?.email,
      firstName: shop?.info?.shop_owner || "Customer",
      orderNumber: "0000000000",
      orderId: "0000000000",
      reviewURL: emailSetting.reviewLinks?.length ? emailSetting.reviewLinks[0].url : "https://trustsync.io",
    };

    const mail: MailgunResponse | null = await handleSendEmail(email, emailSetting, shop);
    console.log("response from mailgun", "Message:" + mail?.message, "Status:" + mail?.status);

    if (!mail || mail.message !== "Queued. Thank you.") {
      return { status: false, message: "Email not queued" };
    }

    return { status: true, message: "Success" };
  } catch (error) {
    console.log(error);

    return { status: false, message: "Error" };
  }
};

// Interface for email sending stats
interface EmailStats {
  emails: number;
  sent: number;
  saveForRetry: number;
}

export const sendEmails = async (): Promise<void> => {
  try {
    console.log("===sendEmails");
    const shopListWhichLimitCross = (await Subscription.find({ valid: false }).select("shop")).map((subscription) => {
      return subscription.shop;
    });

    const shopListWhichDisableSendEmail = (await EmailSettings.find({ active: false }).select("shop")).map(
      (emailSetting) => {
        return emailSetting.shop;
      }
    );

    const shopListWhichUninstalled = (await Shop.find({ isInstalled: false }).select("shop")).map((shop) => {
      return shop.shop;
    });

    const OneHourAgo = new Date(Date.now() - 1000 * 60 * 60);

    // get all emails that has not been sent and sendDate is before today
    const emails = await Email.find({
      sent: false,
      sendDate: { $lte: new Date() },
      // shop: "easytrustdev.myshopify.com",
      shop: { $nin: [...shopListWhichLimitCross, ...shopListWhichDisableSendEmail, ...shopListWhichUninstalled] },
      $or: [{ lastTryDate: { $exists: false } }, { lastTryDate: null }, { lastTryDate: { $lte: OneHourAgo } }],
    })
      .sort({ lastTryDate: 1 })
      .limit(200);

    if (emails.length === 0) {
      console.log("=== nothing to send");
      return;
    }

    // Once every 6 hours
    if (new Date().getHours() % 6 === 0 && new Date().getMinutes() < 10) {
      console.log("===try to send", emails?.length);
    }

    let stats: EmailStats = { emails: emails.length, sent: 0, saveForRetry: 0 };

    // For query optimize purpose
    const shops = new Map<string, IShopDocument>();
    const emailSettings = new Map<string, IEmailSettingsDocument>();

    for (const email of emails) {
      const { shop: shopDB, email: orderEmail } = email;

      // For query optimize check
      const shop = shops.get(email.shop) || (await Shop.findOne({ shop: email.shop }));
      if (shop) shops.set(email.shop, shop);

      const emailSetting = emailSettings.get(email.shop) || (await EmailSettings.findOne({ shop: email.shop }));
      if (emailSetting) emailSettings.set(email.shop, emailSetting);

      const subscription = await Subscription.findOne({ shop: shopDB });

      if (!shop || !emailSetting || !subscription) {
        console.log("===missing shop, emailSetting, or subscription", shopDB);
        continue;
      }

      if (emailSetting.blacklistedEmails?.includes(orderEmail || "")) {
        console.log("===shop blacklistedEmails", emailSetting?.blacklistedEmails, orderEmail);

        // Save last try date
        email.lastTryDate = new Date();
        await email.save();

        stats.saveForRetry++;
        continue;
      }

      if ((subscription.emails || 0) > (subscription.limitEmails || 0)) {
        subscription.valid = false;
        await subscription.save();

        console.log("===surpass email limit", emailSetting?.shop);
        continue;
      }

      const mail: MailgunResponse | null = await handleSendEmail(email, emailSetting, shop);

      console.log("response from mailgun", "Message:" + mail?.message, "Status:" + mail?.status);

      if (!mail || mail.message !== "Queued. Thank you.") {
        return;
      }

      if (mail.message !== "Queued. Thank you.") {
        return;
      }

      email.messageId = mail?.id;
      email.sent = true;
      await email.save();

      subscription.emails = (subscription.emails || 0) + 1;
      await subscription.save();

      stats.sent++;
    }

    console.log("------------------------------------------------");
    console.log("Email sending stats", stats);
    console.log("------------------------------------------------\r\n");
  } catch (err) {
    console.error("=== sendEmails error", err);
  }
};

// Send every one hour interval
cron.schedule("*/60 * * * *", () => {
  console.log("===sendEmails cron");
  sendEmails();
});

const sendSecondEmail = async (): Promise<void> => {
  try {
    console.log("===sendSecondEmail");

    const activeEmailSettings = await EmailSettings.find({ secondEmail: true, active: true });
    if (!activeEmailSettings || activeEmailSettings.length === 0) {
      console.log("===noting to send sendSecondEmail");
      return;
    }

    for (const emailSetting of activeEmailSettings) {
      const { secondWhenToSend } = emailSetting;

      if (!secondWhenToSend?.days) {
        continue;
      }

      const date = addDays(new Date(), -parseInt(secondWhenToSend.days));

      const emails = await Email.find({
        shop: emailSetting.shop,
        sent: true,
        sentSecond: false,
        clicked: false,
        sendDate: { $lte: date },
      });

      if (emails.length === 0) {
        continue;
      }

      for (const email of emails) {
        const { shop: shopDB } = email;

        const shop = await Shop.findOne({ shop: shopDB });
        const subscription = await Subscription.findOne({ shop: shopDB });
        if (!shop || !subscription || !subscription.valid) {
          continue;
        }

        if ((subscription.emails || 0) > (subscription.limitEmails || 0)) {
          subscription.valid = false;
          await subscription.save();
          continue;
        }

        const mail: MailgunResponse | null = await handleSendEmail(email, emailSetting, shop);
        if (!mail) {
          return;
        }

        if (mail.message !== "Queued. Thank you.") {
          return;
        }

        email.messageId = mail?.id;
        email.sentSecond = true;
        await email.save();

        subscription.emails = (subscription.emails || 0) + 1;
        await subscription.save();
      }
    }
  } catch (error) {
    console.error("=== seccondSendEmails error", error);
  }
};

// Send every day 00:00 time (24 hour interval)
cron.schedule("0 0 * * *", () => {
  console.log("===sendSecondEmail cron");
  sendSecondEmail();
});

// Interface for bulk email response
interface BulkEmailResponse {
  status: boolean;
  message: string;
}

/**
 * Sends bulk emails to a list of email IDs for a specific shop domain.
 */
export const sendBulkEmail = async (shopDomain: string, ids: string[]): Promise<BulkEmailResponse> => {
  console.log("===sendBulkEmail");

  try {
    const emails = await Email.find({ _id: { $in: ids } });
    if (!emails || emails.length === 0) {
      return { status: false, message: "No emails found." };
    }

    const shop = await Shop.findOne({ shop: shopDomain });
    const emailSettings = await EmailSettings.findOne({ shop: shopDomain });
    const subscription = await Subscription.findOne({ shop: shopDomain });

    if (!shop || !emailSettings || !subscription) {
      return { status: false, message: "Shop, email settings, or subscription not found." };
    }

    for (let i = 0; i < emails.length; i++) {
      // check if subscription.emails is less than subscription.limitEmails
      if ((subscription.emails || 0) > (subscription.limitEmails || 0)) {
        subscription.valid = false;
        await subscription.save();

        return { status: false, message: "Email limit reached." };
      }

      const email = emails[i];

      const mail: MailgunResponse | null = await handleSendEmail(email, emailSettings, shop);

      console.log({ mail });

      console.log("response from bulkSend mail from email list setting");

      if (!mail || mail.message !== "Queued. Thank you.") {
        console.log("bulkSend mail not send");
        continue;
      }

      email.messageId = mail.id;
      email.sendDate = new Date();
      email.sent = true;

      await email.save();

      // increase subscription.emails count
      subscription.emails = (subscription.emails || 0) + 1;
    }

    await subscription.save();

    return { status: true, message: "Email sent." };
  } catch (error) {
    console.log("error while getting email", error);

    return { status: false, message: "Something went wrong." };
  }
};

// Send weekly report email every Monday at 12:00 PM
cron.schedule("0 12 * * 1", () => {
  console.log(" ----- Activating Send Weekly Report Email Cron ------ ");
  sendWeeklyReportEmail();
});

// cron.schedule("1-59 * * * *", () => {
// console.log(" ----- Activating Send Weekly Report Email Cron ------ ");
// sendWeeklyReportEmail();
// });

// Interface for shop email creation
interface ShopEmailData {
  shop: string;
  email: string;
  sent: boolean;
  sentEmails: number;
  pendingEmails: number;
}

// Interface for email stats
interface ShopEmailStats {
  sentEmails: number;
  pendingEmails: number;
}

// create emails entry to db
const handleCreateShopEmails = async ({
  shop,
  email,
  sent,
  sentEmails,
  pendingEmails,
}: ShopEmailData): Promise<void> => {
  try {
    await ShopEmails.create({
      shop,
      email,
      sentDate: new Date(),
      sent,
      opened: false,
      clicked: false,
      sentEmails,
      pendingEmails,
    });
  } catch (err) {
    console.log("Error creating Shop emails", err);
  }
};

// getting counts for sentEmails and pending emails (last 7 days)
const getEmailStatsForShop = async (shop: IShopDocument): Promise<ShopEmailStats> => {
  const [sentEmails, pendingEmails] = await Promise.all([
    Email.countDocuments({
      shop: shop.shop,
      sent: true,
      sendDate: { $gte: new Date(Date.now() - SEVEN_DAYS_MS) },
    }),
    Email.countDocuments({
      shop: shop.shop,
      sent: false,
      created_at: { $gte: new Date(Date.now() - SEVEN_DAYS_MS) },
    }),
  ]);
  return { sentEmails, pendingEmails };
};

// Interface for weekly report stats
interface WeeklyReportStats {
  total: number;
  processed: number;
  sent: number;
  failed: number;
}

// email processing functions for sending emails
const processShop = async (shop: IShopDocument, stats: WeeklyReportStats): Promise<void> => {
  try {
    const { sentEmails, pendingEmails } = await getEmailStatsForShop(shop);

    console.log(`Email Stats for shop ${shop.shop}: Sent: ${sentEmails}, Pending: ${pendingEmails}`);

    const mail: MailgunResponse | null = await handleWeeklyReportEmail(shop, sentEmails, pendingEmails);

    if (!mail) {
      return;
    }
    const isSent = mail.status === 200;

    await handleCreateShopEmails({
      shop: shop.shop,
      email: shop.info?.email || "",
      sent: isSent,
      sentEmails,
      pendingEmails,
    });

    if (isSent) {
      console.log(`Weekly report email sent to ${shop.shop}`);
      stats.sent++;
    } else {
      console.log(`Failed to send weekly report email to ${shop.shop}`);
      stats.failed++;
    }
  } catch (error) {
    console.error(`Error processing shop ${shop.shop}:`, error);
    await handleCreateShopEmails({
      shop: shop.shop,
      email: shop.info?.email || "",
      sent: false,
      sentEmails: 0,
      pendingEmails: 0,
    });
    stats.failed++;
  }
};

// main function
export const sendWeeklyReportEmail = async (): Promise<void> => {
  try {
    const limit = pLimit(CONCURRENCY_LIMIT);
    let skip = 0;
    let hasMore = true;

    const stats: WeeklyReportStats = { total: 0, processed: 0, sent: 0, failed: 0 };

    while (hasMore) {
      const shops = await Shop.find({ isInstalled: true }).sort({ created_at: -1 }).skip(skip).limit(BATCH_SIZE);

      if (shops.length === 0) break;

      console.log(`Processing batch ${skip / BATCH_SIZE + 1} (Shops ${skip + 1} - ${skip + shops.length})`);
      stats.total += shops.length;

      await Promise.all(shops.map((shop) => limit(() => processShop(shop, stats))));

      stats.processed += shops.length;
      skip += BATCH_SIZE;

      const nextShops = await Shop.find({ isInstalled: true }).sort({ created_at: -1 }).skip(skip).limit(BATCH_SIZE);
      if (nextShops.length > 0) {
        console.log(`Batch completed. Waiting 3 seconds before processing next batch.`);
        await sleep(3000);
      } else {
        hasMore = false;
      }
    }

    console.log("------------------------------------------------");
    console.log("Weekly report email sending stats:", stats);
    console.log("------------------------------------------------\r\n");
  } catch (error) {
    console.error("-------- Send Weekly Report Email Error ---------", error);
  }
};
