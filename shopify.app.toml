# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "87875fce7dc2e9f0753d6eb4b22c1968"
name = "trustsync-react"
handle = "trustsync-react"
application_url = "https://garcia-undertaken-arizona-quizzes.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "easytrustdev.myshopify.com"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders"

[auth]
redirect_urls = [
  "https://garcia-undertaken-arizona-quizzes.trycloudflare.com/auth/callback",
  "https://garcia-undertaken-arizona-quizzes.trycloudflare.com/auth/shopify/callback",
  "https://garcia-undertaken-arizona-quizzes.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-04"

[pos]
embedded = false
