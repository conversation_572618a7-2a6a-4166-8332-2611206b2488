{"name": "shopify-app-template-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "npm run build && node --inspect-brk dist/index.js", "dev": "npm run build && cross-env NODE_ENV=development nodemon --watch . --ext ts --exec \"npm run build && node dist/index.js\" --ignore ./frontend --ignore ./dist", "dev:watch": "concurrently \"tsc --watch && node fix-imports.js\" \"cross-env NODE_ENV=development nodemon --watch dist --exec node dist/index.js\"", "build": "tsc && node fix-imports.js", "build:clean": "rm -rf dist && tsc && node fix-imports.js", "serve": "npm run build && cross-env NODE_ENV=production node dist/index.js", "type-check": "tsc --noEmit", "build:types": "cd ../packages/trustsync-types && pnpm run build", "process:prod:run": "pm2 restart ecosystem.config.cjs"}, "type": "module", "engines": {"node": ">=16.13.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.714.0", "@aws-sdk/lib-storage": "^3.714.0", "@shopify/shopify-app-express": "^5.0.8", "@shopify/shopify-app-session-storage-redis": "^4.2.6", "@shopify/shopify-app-session-storage-sqlite": "^4.0.8", "@trustsync/types": "file:../packages/trustsync-types", "compression": "^1.7.5", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "lodash": "^4.17.21", "mailgun.js": "^10.2.4", "mongoose": "^8.9.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "p-limit": "^6.2.0", "serve-static": "^1.14.1"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/lodash": "^4.17.17", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.1", "@types/node-cron": "^3.0.11", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3", "tsx": "^4.20.1", "typescript": "^5.3.0"}}