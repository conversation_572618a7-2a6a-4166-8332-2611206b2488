#!/usr/bin/env node

import { readdir, readFile, stat, writeFile } from "fs/promises";
import { dirname, extname, join } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function fixImportsInFile(filePath) {
  try {
    const content = await readFile(filePath, "utf8");

    // Fix relative imports that don't have .js extension
    const fixedContent = content.replace(/from\s+["'](\.\.?\/[^"']*?)["']/g, (match, importPath) => {
      // Skip if already has .js/.ts extension or is a directory import
      if (importPath.endsWith(".js") || importPath.endsWith(".ts") || importPath.endsWith("/")) {
        return match;
      }
      // Add .js extension
      return match.replace(importPath, importPath + ".js");
    });

    if (content !== fixedContent) {
      await writeFile(filePath, fixedContent, "utf8");
      console.log(`Fixed imports in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

async function processDirectory(dirPath) {
  try {
    const entries = await readdir(dirPath);

    for (const entry of entries) {
      const fullPath = join(dirPath, entry);
      const stats = await stat(fullPath);

      if (stats.isDirectory()) {
        await processDirectory(fullPath);
      } else if (entry.endsWith(".js")) {
        await fixImportsInFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error processing directory ${dirPath}:`, error.message);
  }
}

async function main() {
  const distPath = join(__dirname, "dist");
  console.log("Fixing import paths in compiled JavaScript files...");
  await processDirectory(distPath);
  console.log("Import path fixing complete!");
}

main().catch(console.error);
